const CONFIG = {
    API_URL: 'https://api.dify.ai/v1/chat-messages',
    API_KEY: 'app-x6ZQorLOeBt7FpSJlkdNIT8b',
};

marked.setOptions({
    breaks: true,
    gfm: true,
    sanitize: false
});


let currentPeople = '';
let conversationId = '';

document.addEventListener('DOMContentLoaded', function() {
    const animalCards = document.querySelectorAll('.animal-card');
    animalCards.forEach(card => {
        card.addEventListener('click', function() {
            const animalName = this.querySelector('h3').textContent;
            openChat(animalName);
        });
        card.style.cursor = 'pointer';
    });
});

function openChat(people) {
    currentPeople = people;
    conversationId = ''; 
    document.getElementById('chatTitle').textContent = `与 ${people} 对话`;
    document.getElementById('chatModal').style.display = 'block';

    const greetings = {
        '松鼠': '🐿️ 你好！我是松鼠。我们是啮齿目松鼠科的小型哺乳动物，全球有超过200种。你想了解关于我们的什么呢？',
        '猫头鹰': '🦉 你好！我是猫头鹰。我们是夜行性鸟类，拥有独特的面部盘和超强的听力。想知道我们如何在夜间捕猎吗？',
        '熊猫': '🐼 你好！我是大熊猫。我们是中国特有的珍稀濒危物种，主要栖息于山区竹林中。有什么关于我们的问题吗？',
        '蜜蜂': '🐝 嗡嗡！我是蜜蜂。我们是重要的传粉昆虫，生活在高度组织化的社会中。想了解我们如何制作蜂蜜吗？',
        '海豚': '🐬 你好！我是海豚。我们是高智商的海洋哺乳动物，使用回声定位导航和捕猎。想知道更多关于我们的生活吗？',
        '狐狸': '🦊 你好！我是狐狸。我们是犬科动物，以敏捷和智慧著称。有什么关于我们的习性想了解的吗？'
    };

    // 清空聊天记录
    const chatMessages = document.getElementById('chatMessages');
    const greeting = greetings[people] || `你好！我是${people}，有什么想了解的吗？`;
    chatMessages.innerHTML = `
        <div class="message assistant">
            <div class="message-content">
                ${greeting}
            </div>
        </div>
    `;

    // 聚焦输入框
    document.getElementById('messageInput').focus();

    document.body.style.overflow = 'hidden';
}

function closeChat() {
    document.getElementById('chatModal').style.display = 'none';
    document.body.style.overflow = 'auto'; // 恢复页面滚动
}

function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}

async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (!message) return;

    // 检查API配置
    if (CONFIG.API_KEY === 'YOUR_API_KEY_HERE') {
        showError('请先配置你的 API Key');
        return;
    }

    // 添加用户消息到聊天界面
    addMessage('user', message);
    messageInput.value = '';

    // 显示加载状态
    showLoading(true);
    setSendButtonState(false);

    try {
        const response = await fetch(CONFIG.API_URL, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${CONFIG.API_KEY}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                inputs: {
                    people: currentPeople
                },
                query: message,
                response_mode: 'blocking',
                conversation_id: conversationId,
                user: 'web-user-' + Date.now(),
                auto_generate_name: true
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // 更新会话ID
        if (data.conversation_id) {
            conversationId = data.conversation_id;
        }

        // 添加AI回复到聊天界面
        addMessage('assistant', data.answer);

    } catch (error) {
        console.error('Error:', error);
        showError('发送消息失败，请检查网络连接和API配置');
    } finally {
        showLoading(false);
        setSendButtonState(true);
    }
}
function addMessage(sender, content) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;

    let processedContent = content;
    if (sender === 'assistant') {
        // 移除思考过程（以特定模式开头的内容）
        processedContent = removeThinkingProcess(content);
        // 渲染 Markdown
        processedContent = marked.parse(processedContent);
    } else {
        // 用户消息进行 HTML 转义
        processedContent = escapeHtml(content);
    }

    messageDiv.innerHTML = `
        <div class="message-content">
            ${processedContent}
        </div>
    `;
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    if (sender === 'assistant') {
        messageDiv.querySelectorAll('pre code').forEach((block) => {
            hljs.highlightElement(block);
        });
    }
}

function removeThinkingProcess(content) {
    // 更强力的思考过程识别和移除
    let result = content;

    // 1. 移除开头的思考段落（通常以特定词汇开头）
    const thinkingStarters = [
        /^嗯，[^]*?(?=\n\n|\n[^\n]*?[：。])/,
        /^让我想想[^]*?(?=\n\n|\n[^\n]*?[：。])/,
        /^这个问题[^]*?(?=\n\n|\n[^\n]*?[：。])/,
        /^用户问的是[^]*?(?=\n\n|\n[^\n]*?[：。])/,
        /^用户[^]*?(?=\n\n|\n[^\n]*?[：。])/,
        /^考虑到[^]*?(?=\n\n|\n[^\n]*?[：。])/,
        /^需要[^]*?(?=\n\n|\n[^\n]*?[：。])/,
        /^要注意[^]*?(?=\n\n|\n[^\n]*?[：。])/,
        /^上次[^]*?(?=\n\n|\n[^\n]*?[：。])/,
        /^可以[^]*?(?=\n\n|\n[^\n]*?[：。])/
    ];

    thinkingStarters.forEach(pattern => {
        result = result.replace(pattern, '');
    });

    // 2. 移除包含分析性关键词的整个段落
    const analyticalPatterns = [
        /[^]*?用户可能[^]*?(?=\n\n|$)/g,
        /[^]*?用户或许[^]*?(?=\n\n|$)/g,
        /[^]*?用户想[^]*?(?=\n\n|$)/g,
        /[^]*?这有点棘手[^]*?(?=\n\n|$)/g,
        /[^]*?需要强调[^]*?(?=\n\n|$)/g,
        /[^]*?要澄清[^]*?(?=\n\n|$)/g,
        /[^]*?但需要[^]*?(?=\n\n|$)/g,
        /[^]*?可以描述[^]*?(?=\n\n|$)/g,
        /[^]*?加入.*?细节[^]*?(?=\n\n|$)/g,
        /[^]*?避免.*?术语[^]*?(?=\n\n|$)/g
    ];

    analyticalPatterns.forEach(pattern => {
        result = result.replace(pattern, '');
    });

    const answerMarkers = [
        /作为.*?[，,][^]*$/,  // "作为...，" 后面的内容通常是回答
        /^[^]*?(?=作为.*?[，,])/  // 移除"作为"之前的思考内容
    ];

    const asMatch = result.match(/作为.*$/s);
    if (asMatch) {
        result = asMatch[0];
    }

    result = result.replace(/\n\s*\n\s*\n/g, '\n\n');
    result = result.trim();

    if (result.length < 20) {
        // 按段落分割，取最后几个有实质内容的段落
        const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 10);
        if (paragraphs.length > 0) {
            // 取最后1-2个段落作为回答
            result = paragraphs.slice(-2).join('\n\n');
        } else {
            result = content; // 如果都失败了，返回原内容
        }
    }

    return result;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showLoading(show) {
    const loading = document.getElementById('loading');
    loading.style.display = show ? 'block' : 'none';
}

function setSendButtonState(enabled) {
    const sendButton = document.getElementById('sendButton');
    sendButton.disabled = !enabled;
    sendButton.textContent = enabled ? '发送' : '发送中...';
}

function showError(message) {
    const chatMessages = document.getElementById('chatMessages');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error';
    errorDiv.textContent = message;
    chatMessages.appendChild(errorDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

window.onclick = function(event) {
    const modal = document.getElementById('chatModal');
    if (event.target === modal) {
        closeChat();
    }
}