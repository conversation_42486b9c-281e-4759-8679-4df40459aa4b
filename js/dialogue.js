document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('dialogueModal');
    const closeBtn = document.querySelector('.close-modal');
    const dialogueBtns = document.querySelectorAll('.dialogue-btn');
    const modalAnimalImage = document.getElementById('modalAnimalImage');
    const modalAnimalName = document.getElementById('modalAnimalName');
    const dialogueMessages = document.getElementById('dialogueMessages');
    const userMessageInput = document.getElementById('userMessage');
    const sendMessageBtn = document.getElementById('sendMessage');
    
    let currentAnimal = '';
    
    // 动物回应的预设对话内容
    const animalResponses = {
        elephant: {
            greeting: "你好！我是非洲象。你想了解关于我的什么呢？",
            food: "我每天需要摄入大约150-300公斤的食物和70-90升的水。我主要吃草、树叶、树皮、水果和根茎。",
            habitat: "我生活在非洲的热带森林、草原和稀树草原地区。我们的栖息地范围很广，但正在不断减少。",
            family: "我们生活在由资深雌象领导的家族群体中，通常包括相关的雌象和它们的幼崽。成年雄象则常常独自行动或形成小的单性群体。",
            default: "这是一个有趣的问题！作为非洲象，我对于自然界有着深刻的理解。我们是地球上最大的陆地动物，有着强大的记忆力和复杂的社交结构。"
        },
        lion: {
            greeting: "吼！我是狮子，草原之王。有什么想问我的吗？",
            food: "作为顶级掠食者，我主要捕食斑马、角马、羚羊和水牛等大型哺乳动物。一顿饱餐后，我可能几天不需要再进食。",
            habitat: "我生活在非洲的草原、热带稀树草原和灌木丛地区。我们适应了开阔的地形，这有利于我们的狩猎。",
            family: "我们生活在被称为'骄傲'的社会群体中。通常由几个相关的雌狮、它们的幼崽和少数成年雄狮组成。雌狮负责大部分的狩猎活动。",
            default: "作为狮子，我的生活充满了挑战和荣耀。我们的吼叫声可以传播到8公里外，这是我们标记领地和互相交流的方式。"
        },
        panda: {
            greeting: "你好呀！我是大熊猫，很高兴认识你！",
            food: "我几乎完全以竹子为食，每天要吃10-40公斤竹子！尽管我属于食肉目，但我99%的食物都是竹子。",
            habitat: "我生活在中国的四川、陕西和甘肃的高山竹林中，海拔1200-3100米之间。我喜欢凉爽潮湿的环境。",
            family: "我是比较独居的动物，除了交配季节外，我大部分时间都独自度过。母熊猫通常每两年生育一次，幼崽非常小，只有巴掌大小。",
            default: "作为中国的国宝，我的生活其实很悠闲。我每天大部分时间都在吃竹子和休息。我们熊猫看起来胖胖的，但其实非常灵活，能够轻松爬树。"
        },
        polar_bear: {
            greeting: "你好！我是来自北极的白熊。你想知道什么？",
            food: "我主要以海豹为食，特别是环斑海豹。我会在冰上静静等待，当海豹浮出水面呼吸时将其捕获。在食物稀缺的季节，我也会吃鱼、海鸟和浆果。",
            habitat: "我生活在北极地区，主要在海冰上活动。随着气候变化，我的栖息地正在迅速减少，这对我们的生存构成了威胁。",
            family: "母熊通常有1-3只幼崽，它们会和母亲一起生活2-3年学习生存技能。我们大部分时间是独居的，但有时会聚集在食物丰富的地区。",
            default: "作为北极的顶级捕食者，我进化出了许多适应严寒环境的特点。我的皮毛看起来是白色的，但实际上是透明的，皮肤是黑色的。我是出色的游泳者，可以连续游几个小时。"
        },
        sea_turtle: {
            greeting: "嗨！我是海龟，欢迎来到海洋世界！",
            food: "不同种类的海龟有不同的饮食习惯。绿海龟主要吃海草和藻类，而玳瑁喜欢吃海绵和软体动物。我最喜欢的食物是水母！",
            habitat: "我在世界各大洋的温暖和温带海域生活，从海岸线到开阔的海洋。我只有在产卵时才会上岸，雌性会回到出生的海滩产卵。",
            family: "海龟是独居动物。雌龟会在沙滩上挖一个洞产下多达100-150颗蛋，然后离开。孵化的小海龟必须自己找到通往大海的路。",
            default: "作为古老的海洋爬行动物，我们海龟已经在地球上生活了超过1亿年！我们有一个坚硬的壳保护我们，可以长途迁徙，有些海龟一生可以游过整个大洋。"
        }
    };
    
    // 打开对话模态框
    dialogueBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const animal = this.getAttribute('data-animal');
            const animalCard = this.closest('.animal-card');
            const animalName = animalCard.querySelector('h3').textContent;
            const animalImageSrc = animalCard.querySelector('img').src;
            
            currentAnimal = animal;
            modalAnimalName.textContent = animalName;
            modalAnimalImage.src = animalImageSrc;
            modalAnimalImage.alt = animalName;
            
            // 清空之前的对话
            dialogueMessages.innerHTML = '';
            
            // 添加欢迎消息
            addMessage(animalResponses[animal].greeting, 'animal');
            
            modal.style.display = 'block';
            
            // 防止页面滚动
            document.body.style.overflow = 'hidden';
        });
    });
    
    // 关闭对话模态框
    closeBtn.addEventListener('click', closeModal);
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeModal();
        }
    });
    
    // 发送消息
    sendMessageBtn.addEventListener('click', sendMessage);
    
    // 按回车发送消息
    userMessageInput.addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
            sendMessage();
        }
    });
    
    // 发送消息函数
    function sendMessage() {
        const message = userMessageInput.value.trim();
        if (message) {
            // 添加用户消息
            addMessage(message, 'user');
            
            // 清空输入框
            userMessageInput.value = '';
            
            // 模拟动物思考时间
            setTimeout(function() {
                // 获取动物回应
                const response = getAnimalResponse(currentAnimal, message.toLowerCase());
                
                // 添加动物回应
                addMessage(response, 'animal');
                
                // 滚动到最新消息
                dialogueMessages.scrollTop = dialogueMessages.scrollHeight;
            }, 1000);
        }
    }
    
    // 添加消息到对话框
    function addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message');
        messageDiv.classList.add(sender + '-message');
        messageDiv.textContent = text;
        
        dialogueMessages.appendChild(messageDiv);
        
        // 滚动到最新消息
        dialogueMessages.scrollTop = dialogueMessages.scrollHeight;
    }
    
    // 获取动物回应
    function getAnimalResponse(animal, message) {
        const responses = animalResponses[animal];
        
        if (message.includes('吃') || message.includes('食物') || message.includes('食性')) {
            return responses.food;
        } else if (message.includes('住') || message.includes('栖息') || message.includes('生活') || message.includes('环境')) {
            return responses.habitat;
        } else if (message.includes('家庭') || message.includes('群体') || message.includes('繁殖') || message.includes('幼崽')) {
            return responses.family;
        } else {
            return responses.default;
        }
    }
    
    // 关闭模态框函数
    function closeModal() {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // 恢复页面滚动
    }
}); 