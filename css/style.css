* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
}

body {
    background-color: #f1f8e9;
    color: #333;
    line-height: 1.6;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

header {
    background-color: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo a {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: 10px;
}

.logo span {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2e7d32;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-menu li {
    position: relative;
    margin-left: 20px;
}

.nav-menu li a {
    display: block;
    padding: 10px;
    color: #555;
    font-size: 1rem;
    transition: color 0.3s;
}

.nav-menu li a:hover, 
.nav-menu li a.active {
    color: #2e7d32;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #fff;
    min-width: 180px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu li a {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-menu li:last-child a {
    border-bottom: none;
}

.search-icon {
    font-size: 1.2rem;
    color: #555;
    cursor: pointer;
    transition: color 0.3s;
}

.search-icon:hover {
    color: #2e7d32;
}

.banner {
    position: relative;
    width: 100%;
    height: 500px;
    background-image: url('../images/banner.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
}

.banner-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.banner-content h1 {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-description {
    font-size: 1.2rem;
    margin-bottom: 30px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.banner-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary {
    background-color: #2e7d32;
    color: white;
    border: 2px solid #2e7d32;
}

.btn-primary:hover {
    background-color: #1b5e20;
    border-color: #1b5e20;
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: white;
    color: #2e7d32;
}

main {
    position: relative;
    z-index: 10;
}

.featured-section {
    padding: 60px 0;
    text-align: center;
}

.section-title {
    font-size: 2.2rem;
    color: #2e7d32;
    margin-bottom: 30px;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    width: 70%;
    height: 3px;
    background-color: #2e7d32;
    bottom: -10px;
    left: 15%;
}

.featured-content {
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

.featured-content p {
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    color: #555;
    cursor: pointer;
    transition: color 0.3s;
}

.mobile-menu-toggle:hover {
    color: #2e7d32;
}

.animal-classification {
    padding: 30px 0;
    background-color: #f8f9fa;
    width: 100%;
}

.animal-classification .container {
    max-width: 1400px; 
    width: 95%;
}

.animal-classification .section-title {
    margin-bottom: 20px;
    text-align: center;
}

.classification-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-top: 15px;
    gap: 30px;
}

.classification-text {
    flex: 2;
    min-width: 300px;
    text-align: justify;
}

.classification-text p {
    margin-bottom: 12px;
    line-height: 1.5;
}

.classification-text p:last-child {
    margin-bottom: 0;
}

.classification-text strong {
    color: #2e7d32;
    font-weight: bold;
}

.classification-image {
    flex: 1;
    min-width: 300px;
    max-width: 35%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.classification-image img {
    width: 100%;
    max-height: 320px;
    object-fit: contain;
}



footer {
    background-color: #333;
    color: #fff;
    padding: 20px 0;
    text-align: center;
    margin-top: 30px;
}

.footer-content {
    display: flex;
    justify-content: center;
    align-items: center;
}

.copyright {
    font-size: 0.9rem;
    opacity: 0.8;
}


.dialogue-banner {
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/banner2.png');
    height: 400px;
}

.dialogue-intro {
    padding: 50px 0 30px;
    text-align: center;
}

.animal-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin: 30px 0;
}

.animal-card {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    padding: 0;
    height: 180px;
}

.animal-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
}

.animal-image {
    flex: 0 0 150px;
    height: 180px;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.animal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.animal-card:hover .animal-image img {
    transform: scale(1.05);
}

.animal-info {
    flex: 1;
    padding: 20px;
    height: 150px; 
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.animal-info h3 {
    font-size: 1.5rem;
    color: #2e7d32;
    margin-bottom: 8px;
}

.animal-info p {
    font-size: 0.95rem;
    color: #666;
    line-height: 1.5;
}

.left-image {
    flex-direction: row;
}

.right-image {
    flex-direction: row-reverse;
}



/* 对话模态框样式 */
.dialogue-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    overflow: auto;
}

.modal-content {
    background-color: #fff;
    margin: 50px auto;
    width: 80%;
    max-width: 900px;
    border-radius: 10px;
    position: relative;
    animation: modalOpen 0.4s;
}

@keyframes modalOpen {
    from {opacity: 0; transform: translateY(-50px);}
    to {opacity: 1; transform: translateY(0);}
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    color: #777;
    cursor: pointer;
    transition: color 0.3s;
}

.close-modal:hover {
    color: #333;
}

.animal-dialogue {
    display: flex;
    flex-wrap: wrap;
    padding: 25px;
}

.animal-profile {
    width: 30%;
    text-align: center;
    padding-right: 20px;
}

.animal-profile img {
    width: 100%;
    max-height: 250px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 15px;
}

.animal-profile h3 {
    font-size: 1.4rem;
    color: #2e7d32;
}

.dialogue-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 300px;
}

.dialogue-messages {
    flex: 1;
    min-height: 300px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #f9f9f9;
}

.message {
    margin-bottom: 15px;
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 80%;
    word-wrap: break-word;
}

.user-message {
    background-color: #e3f2fd;
    align-self: flex-end;
    margin-left: auto;
    border-bottom-right-radius: 5px;
}

.animal-message {
    background-color: #f1f8e9;
    align-self: flex-start;
    border-bottom-left-radius: 5px;
}

.dialogue-input {
    display: flex;
    gap: 10px;
}

.dialogue-input input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 1rem;
}

.dialogue-input button {
    background-color: #2e7d32;
    color: white;
    border: none;
    padding: 0 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.dialogue-input button:hover {
    background-color: #1b5e20;
}



.avatar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.avatar-item {
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.avatar-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

.avatar-item .animal-image img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    margin: 20px auto 10px;
    display: block;
}

/* 对话框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal .modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 25px 50px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
    color: white;
    padding: 20px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5em;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 0.7;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    line-height: 1.5;
}

.message-content h1, .message-content h2, .message-content h3,
.message-content h4, .message-content h5, .message-content h6 {
    margin: 10px 0 5px 0;
    font-weight: bold;
}

.message-content h1 { font-size: 1.5em; }
.message-content h2 { font-size: 1.3em; }
.message-content h3 { font-size: 1.1em; }

.message-content p {
    margin: 8px 0;
}

.message-content ul, .message-content ol {
    margin: 8px 0;
    padding-left: 20px;
}

.message-content li {
    margin: 4px 0;
}

.message-content blockquote {
    border-left: 4px solid #2e7d32;
    margin: 10px 0;
    padding: 8px 12px;
    background: rgba(46, 125, 50, 0.1);
    font-style: italic;
}

.message-content code {
    background: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.message-content pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 12px;
    overflow-x: auto;
    margin: 10px 0;
}

.message-content pre code {
    background: none;
    padding: 0;
}

.message-content strong {
    font-weight: bold;
}

.message-content em {
    font-style: italic;
}

.message-content hr {
    border: none;
    border-top: 1px solid #e0e0e0;
    margin: 15px 0;
}

.message-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

.message-content th, .message-content td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.message-content th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.message.user .message-content {
    background: #2e7d32;
    color: white;
    border-bottom-right-radius: 5px;
}

.message.assistant .message-content {
    background: white;
    color: #333;
    border: 1px solid #e0e0e0;
    border-bottom-left-radius: 5px;
}

.chat-input {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background: white;
    border-radius: 0 0 15px 15px;
}

.input-group {
    display: flex;
    gap: 10px;
}

.input-group input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    outline: none;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    border-color: #2e7d32;
}

.input-group button {
    padding: 12px 24px;
    background: #2e7d32;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

.input-group button:hover:not(:disabled) {
    background: #1b5e20;
}

.input-group button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.loading {
    display: none;
    text-align: center;
    padding: 10px;
    color: #666;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2e7d32;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}