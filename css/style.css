/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
}

body {
    background-color: #f1f8e9;
    color: #333;
    line-height: 1.6;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

/* 导航栏样式 */
header {
    background-color: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo a {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: 10px;
}

.logo span {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2e7d32;
}

/* 导航菜单 */
.nav-menu {
    display: flex;
    align-items: center;
}

.nav-menu li {
    position: relative;
    margin-left: 20px;
}

.nav-menu li a {
    display: block;
    padding: 10px;
    color: #555;
    font-size: 1rem;
    transition: color 0.3s;
}

.nav-menu li a:hover, 
.nav-menu li a.active {
    color: #2e7d32;
}

/* 下拉菜单 */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #fff;
    min-width: 180px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu li a {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-menu li:last-child a {
    border-bottom: none;
}

/* 搜索图标 */
.search-icon {
    font-size: 1.2rem;
    color: #555;
    cursor: pointer;
    transition: color 0.3s;
}

.search-icon:hover {
    color: #2e7d32;
}

/* Banner样式 */
.banner {
    position: relative;
    width: 100%;
    height: 500px;
    background-image: url('../images/banner.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
}

.banner-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.banner-content h1 {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-description {
    font-size: 1.2rem;
    margin-bottom: 30px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.banner-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary {
    background-color: #2e7d32;
    color: white;
    border: 2px solid #2e7d32;
}

.btn-primary:hover {
    background-color: #1b5e20;
    border-color: #1b5e20;
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: white;
    color: #2e7d32;
}

/* 波浪过渡效果 */
.wave-container {
    position: relative;
    width: 100%;
    margin-top: -150px;
    z-index: 5;
    overflow: hidden;
}

.wave {
    width: 100%;
    height: auto;
    display: block;
}

/* 主要内容区域 */
main {
    position: relative;
    z-index: 10;
}

.featured-section {
    padding: 60px 0;
    text-align: center;
}

.section-title {
    font-size: 2.2rem;
    color: #2e7d32;
    margin-bottom: 30px;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    width: 70%;
    height: 3px;
    background-color: #2e7d32;
    bottom: -10px;
    left: 15%;
}

.featured-content {
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

.featured-content p {
    margin-bottom: 20px;
    font-size: 1.1rem;
}

/* 移动菜单切换按钮 */
.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    color: #555;
    cursor: pointer;
    transition: color 0.3s;
}

.mobile-menu-toggle:hover {
    color: #2e7d32;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 80%;
        height: 100vh;
        background-color: #fff;
        flex-direction: column;
        align-items: flex-start;
        padding: 20px;
        transition: left 0.3s;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu li {
        margin: 10px 0;
        width: 100%;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        display: none;
    }
    
    .dropdown.active .dropdown-menu {
        display: block;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .banner {
        height: 350px;
    }
    
    .banner-content h1 {
        font-size: 2rem;
    }
    
    .banner-description {
        font-size: 1rem;
        margin-bottom: 20px;
    }
    
    .banner-buttons {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .wave-container {
        margin-top: -100px;
    }
} 

/* 动物分类模块 */
.animal-classification {
    padding: 30px 0;
    background-color: #f8f9fa;
    width: 100%;
}

.animal-classification .container {
    max-width: 1400px; /* 比标准容器更宽 */
    width: 95%;
}

.animal-classification .section-title {
    margin-bottom: 20px;
    text-align: center;
}

.classification-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-top: 15px;
    gap: 30px;
}

.classification-text {
    flex: 2;
    min-width: 300px;
    text-align: justify;
}

.classification-text p {
    margin-bottom: 12px;
    line-height: 1.5;
}

.classification-text p:last-child {
    margin-bottom: 0;
}

.classification-text strong {
    color: #2e7d32;
    font-weight: bold;
}

.classification-image {
    flex: 1;
    min-width: 300px;
    max-width: 35%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.classification-image img {
    width: 100%;
    max-height: 320px;
    object-fit: contain;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .classification-content {
        flex-direction: column;
    }
    
    .classification-text,
    .classification-image {
        flex: none;
        width: 100%;
        max-width: 100%;
    }
    
    .classification-image {
        margin-top: 15px;
        order: -1; /* 在移动视图中图片显示在文字上方 */
    }
    
    .classification-image img {
        max-height: 220px;
    }
} 

/* 页脚样式 */
footer {
    background-color: #333;
    color: #fff;
    padding: 20px 0;
    text-align: center;
    margin-top: 30px;
}

.footer-content {
    display: flex;
    justify-content: center;
    align-items: center;
}

.copyright {
    font-size: 0.9rem;
    opacity: 0.8;
}

@media (max-width: 768px) {
    footer {
        padding: 15px 0;
        margin-top: 20px;
    }
    
    .copyright {
        font-size: 0.8rem;
    }
} 

/* 动物对话页面样式 */
.dialogue-banner {
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/dialogue-banner.jpg');
    height: 400px;
}

.dialogue-intro {
    padding: 50px 0 30px;
    text-align: center;
}

.animal-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin-bottom: 50px;
}

.animal-card {
    display: flex;
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.animal-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.animal-image {
    width: 35%;
    min-width: 250px;
    overflow: hidden;
}

.animal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.animal-card:hover .animal-image img {
    transform: scale(1.05);
}

.animal-info {
    flex: 1;
    padding: 25px;
    display: flex;
    flex-direction: column;
}

.animal-info h3 {
    font-size: 1.6rem;
    color: #2e7d32;
    margin-bottom: 15px;
}

.animal-info p {
    margin-bottom: 20px;
    line-height: 1.6;
    flex-grow: 1;
}

.dialogue-btn {
    align-self: flex-start;
    background-color: #2e7d32;
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.dialogue-btn:hover {
    background-color: #1b5e20;
}

/* 对话模态框样式 */
.dialogue-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    overflow: auto;
}

.modal-content {
    background-color: #fff;
    margin: 50px auto;
    width: 80%;
    max-width: 900px;
    border-radius: 10px;
    position: relative;
    animation: modalOpen 0.4s;
}

@keyframes modalOpen {
    from {opacity: 0; transform: translateY(-50px);}
    to {opacity: 1; transform: translateY(0);}
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    color: #777;
    cursor: pointer;
    transition: color 0.3s;
}

.close-modal:hover {
    color: #333;
}

.animal-dialogue {
    display: flex;
    flex-wrap: wrap;
    padding: 25px;
}

.animal-profile {
    width: 30%;
    text-align: center;
    padding-right: 20px;
}

.animal-profile img {
    width: 100%;
    max-height: 250px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 15px;
}

.animal-profile h3 {
    font-size: 1.4rem;
    color: #2e7d32;
}

.dialogue-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 300px;
}

.dialogue-messages {
    flex: 1;
    min-height: 300px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #f9f9f9;
}

.message {
    margin-bottom: 15px;
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 80%;
    word-wrap: break-word;
}

.user-message {
    background-color: #e3f2fd;
    align-self: flex-end;
    margin-left: auto;
    border-bottom-right-radius: 5px;
}

.animal-message {
    background-color: #f1f8e9;
    align-self: flex-start;
    border-bottom-left-radius: 5px;
}

.dialogue-input {
    display: flex;
    gap: 10px;
}

.dialogue-input input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 1rem;
}

.dialogue-input button {
    background-color: #2e7d32;
    color: white;
    border: none;
    padding: 0 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.dialogue-input button:hover {
    background-color: #1b5e20;
}

/* 响应式设计 */
@media (max-width: 900px) {
    .animal-card {
        flex-direction: column;
    }
    
    .animal-image {
        width: 100%;
        height: 250px;
    }
    
    .animal-profile {
        width: 100%;
        padding-right: 0;
        margin-bottom: 20px;
    }
    
    .animal-profile img {
        max-height: 200px;
    }
    
    .dialogue-container {
        width: 100%;
    }
}

@media (max-width: 600px) {
    .modal-content {
        width: 95%;
        margin: 30px auto;
    }
    
    .dialogue-messages {
        min-height: 250px;
    }
    
    .animal-image {
        height: 200px;
    }
} 