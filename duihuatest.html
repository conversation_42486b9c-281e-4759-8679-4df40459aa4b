<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify 对话应用测试</title>
    <!-- 引入 marked.js 用于 Markdown 渲染 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- 引入 highlight.js 用于代码高亮 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/default.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .avatar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .avatar-item {
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-radius: 15px;
            overflow: hidden;
            background: #f8f9fa;
            padding: 20px;
        }

        .avatar-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .avatar-item img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 10px;
        }

        .avatar-item h3 {
            color: #333;
            font-size: 1.2em;
            margin-bottom: 5px;
        }

        .avatar-item p {
            color: #666;
            font-size: 0.9em;
        }

        /* 对话框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5em;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .close:hover {
            opacity: 0.7;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.assistant {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        /* Markdown 样式 */
        .message-content h1, .message-content h2, .message-content h3,
        .message-content h4, .message-content h5, .message-content h6 {
            margin: 10px 0 5px 0;
            font-weight: bold;
        }

        .message-content h1 { font-size: 1.5em; }
        .message-content h2 { font-size: 1.3em; }
        .message-content h3 { font-size: 1.1em; }

        .message-content p {
            margin: 8px 0;
        }

        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 4px 0;
        }

        .message-content blockquote {
            border-left: 4px solid #007bff;
            margin: 10px 0;
            padding: 8px 12px;
            background: rgba(0, 123, 255, 0.1);
            font-style: italic;
        }

        .message-content code {
            background: #f1f3f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .message-content pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }

        .message-content pre code {
            background: none;
            padding: 0;
        }

        .message-content strong {
            font-weight: bold;
        }

        .message-content em {
            font-style: italic;
        }

        .message-content hr {
            border: none;
            border-top: 1px solid #e0e0e0;
            margin: 15px 0;
        }

        .message-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }

        .message-content th, .message-content td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .message-content th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
            border-radius: 0 0 15px 15px;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .input-group input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus {
            border-color: #007bff;
        }

        .input-group button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }

        .input-group button:hover:not(:disabled) {
            background: #0056b3;
        }

        .input-group button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐾 动物学者对话</h1>
        <p style="margin-bottom: 30px; color: #666; font-size: 1.1em;">点击下方动物学者头像，与它们进行有趣的对话</p>
        
        <div class="avatar-grid">
            <div class="avatar-item" onclick="openChat('松鼠')">
                <img src="https://via.placeholder.com/100x100/8B4513/ffffff?text=🐿️" alt="松鼠爱因斯坦">
                <h3>松鼠</h3>
            </div>
            <div class="avatar-item" onclick="openChat('猫头鹰')">
                <img src="https://via.placeholder.com/100x100/4B0082/ffffff?text=🦉" alt="猫头鹰莎士比亚">
                <h3>猫头鹰</h3>
            </div>
            <div class="avatar-item" onclick="openChat('熊猫')">
                <img src="https://via.placeholder.com/100x100/2F4F2F/ffffff?text=🐼" alt="熊猫孔子">
                <h3>熊猫</h3>
            </div>
            <div class="avatar-item" onclick="openChat('蜜蜂')">
                <img src="https://via.placeholder.com/100x100/FFD700/000000?text=🐝" alt="蜜蜂居里夫人">
                <h3>蜜蜂</h3>
            </div>
            <div class="avatar-item" onclick="openChat('海豚')">
                <img src="https://via.placeholder.com/100x100/1E90FF/ffffff?text=🐬" alt="海豚达芬奇">
                <h3>海豚</h3>
            </div>
            <div class="avatar-item" onclick="openChat('狐狸')">
                <img src="https://via.placeholder.com/100x100/FF4500/ffffff?text=🦊" alt="狐狸福尔摩斯">
                <h3>狐狸</h3>
            </div>
        </div>
    </div>

    <!-- 对话框模态窗口 -->
    <div id="chatModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="chatTitle">与 AI 对话</h2>
                <span class="close" onclick="closeChat()">&times;</span>
            </div>
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant">
                        <div class="message-content">
                            你好！我是你选择的角色，有什么想聊的吗？
                        </div>
                    </div>
                </div>
                <div class="loading" id="loading">正在思考中...</div>
                <div class="chat-input">
                    <div class="input-group">
                        <input type="text" id="messageInput" placeholder="输入你的问题..." onkeypress="handleKeyPress(event)">
                        <button onclick="sendMessage()" id="sendButton">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 配置信息 - 请替换为你的实际配置
        const CONFIG = {
            API_URL: 'https://api.dify.ai/v1/chat-messages',
            API_KEY: 'app-x6ZQorLOeBt7FpSJlkdNIT8b', 
        };

        // 配置 marked.js
        marked.setOptions({
            breaks: true,
            gfm: true,
            sanitize: false
        });

        // 使用提
        console.log('使用说明：');
        console.log('1. API Key 已配置');
        console.log('2. 确保你的 Dify 应用已配置 people 变量');
        console.log('3. 点击角色头像开始对话！');
        console.log('4. 支持 Markdown 语法渲染和思考过程过滤');

        let currentPeople = '';
        let conversationId = '';

        function openChat(people) {
            currentPeople = people;
            conversationId = ''; // 重置会话ID
            document.getElementById('chatTitle').textContent = `与 ${people} 对话`;
            document.getElementById('chatModal').style.display = 'block';

            // 根据不同动物角色设置个性化的开场白
            const greetings = {
                '松鼠爱因斯坦': '🐿️ 你好！我是松鼠爱因斯坦，正在我的橡树实验室里研究相对论。想聊聊物理学吗？',
                '猫头鹰莎士比亚': '🦉 午夜好！我是猫头鹰莎士比亚，月光下的诗人。想听听关于文学和戏剧的故事吗？',
                '熊猫孔子': '🐼 你好！我是熊猫孔子，在竹林中思考人生哲理。有什么困惑想要探讨吗？',
                '蜜蜂居里夫人': '🐝 嗡嗡！我是蜜蜂居里夫人，在蜂巢实验室研究放射性花粉。想了解科学发现吗？',
                '海豚达芬奇': '🐬 你好！我是海豚达芬奇，在海洋中创作艺术和发明。想聊聊艺术与科学吗？',
                '狐狸福尔摩斯': '🦊 晚上好！我是狐狸福尔摩斯，森林中的名侦探。有什么谜题需要解决吗？'
            };

            // 清空聊天记录
            const chatMessages = document.getElementById('chatMessages');
            const greeting = greetings[people] || `你好！我是${people}，有什么想聊的吗？`;
            chatMessages.innerHTML = `
                <div class="message assistant">
                    <div class="message-content">
                        ${greeting}
                    </div>
                </div>
            `;

            // 聚焦输入框
            document.getElementById('messageInput').focus();
        }

        function closeChat() {
            document.getElementById('chatModal').style.display = 'none';
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) return;
            
            // 检查API配置
            if (CONFIG.API_KEY === 'YOUR_API_KEY_HERE') {
                showError('请先配置你的 API Key');
                return;
            }

            // 添加用户消息到聊天界面
            addMessage('user', message);
            messageInput.value = '';
            
            // 显示加载状态
            showLoading(true);
            setSendButtonState(false);

            try {
                const response = await fetch(CONFIG.API_URL, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${CONFIG.API_KEY}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        inputs: {
                            people: currentPeople
                        },
                        query: message,
                        response_mode: 'blocking',
                        conversation_id: conversationId,
                        user: 'web-user-' + Date.now(),
                        auto_generate_name: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                // 更新会话ID
                if (data.conversation_id) {
                    conversationId = data.conversation_id;
                }
                
                // 添加AI回复到聊天界面
                addMessage('assistant', data.answer);
                
            } catch (error) {
                console.error('Error:', error);
                showError('发送消息失败，请检查网络连接和API配置');
            } finally {
                showLoading(false);
                setSendButtonState(true);
            }
        }

        function addMessage(sender, content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            // 如果是助手消息，使用 Markdown 渲染
            let processedContent = content;
            if (sender === 'assistant') {
                // 移除思考过程（以特定模式开头的内容）
                processedContent = removeThinkingProcess(content);
                // 渲染 Markdown
                processedContent = marked.parse(processedContent);
            } else {
                // 用户消息进行 HTML 转义
                processedContent = escapeHtml(content);
            }

            messageDiv.innerHTML = `
                <div class="message-content">
                    ${processedContent}
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // 如果包含代码块，进行语法高亮
            if (sender === 'assistant') {
                messageDiv.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightElement(block);
                });
            }
        }

        function removeThinkingProcess(content) {
            // 更强力的思考过程识别和移除
            let result = content;

            // 1. 移除开头的思考段落（通常以特定词汇开头）
            const thinkingStarters = [
                /^嗯，[^]*?(?=\n\n|\n[^\n]*?[：。])/,
                /^让我想想[^]*?(?=\n\n|\n[^\n]*?[：。])/,
                /^这个问题[^]*?(?=\n\n|\n[^\n]*?[：。])/,
                /^用户问的是[^]*?(?=\n\n|\n[^\n]*?[：。])/,
                /^用户[^]*?(?=\n\n|\n[^\n]*?[：。])/,
                /^考虑到[^]*?(?=\n\n|\n[^\n]*?[：。])/,
                /^需要[^]*?(?=\n\n|\n[^\n]*?[：。])/,
                /^要注意[^]*?(?=\n\n|\n[^\n]*?[：。])/,
                /^上次[^]*?(?=\n\n|\n[^\n]*?[：。])/,
                /^可以[^]*?(?=\n\n|\n[^\n]*?[：。])/
            ];

            thinkingStarters.forEach(pattern => {
                result = result.replace(pattern, '');
            });

            // 2. 移除包含分析性关键词的整个段落
            const analyticalPatterns = [
                /[^]*?用户可能[^]*?(?=\n\n|$)/g,
                /[^]*?用户或许[^]*?(?=\n\n|$)/g,
                /[^]*?用户想[^]*?(?=\n\n|$)/g,
                /[^]*?这有点棘手[^]*?(?=\n\n|$)/g,
                /[^]*?需要强调[^]*?(?=\n\n|$)/g,
                /[^]*?要澄清[^]*?(?=\n\n|$)/g,
                /[^]*?但需要[^]*?(?=\n\n|$)/g,
                /[^]*?可以描述[^]*?(?=\n\n|$)/g,
                /[^]*?加入.*?细节[^]*?(?=\n\n|$)/g,
                /[^]*?避免.*?术语[^]*?(?=\n\n|$)/g
            ];

            analyticalPatterns.forEach(pattern => {
                result = result.replace(pattern, '');
            });

            // 3. 查找并保留实际回答内容（通常在思考后出现）
            // 寻找明确的回答开始标志
            const answerMarkers = [
                /作为.*?[，,][^]*$/,  // "作为...，" 后面的内容通常是回答
                /^[^]*?(?=作为.*?[，,])/  // 移除"作为"之前的思考内容
            ];

            // 如果找到"作为"开头的回答，提取该部分
            const asMatch = result.match(/作为.*$/s);
            if (asMatch) {
                result = asMatch[0];
            }

            // 4. 清理多余的空行和空白
            result = result.replace(/\n\s*\n\s*\n/g, '\n\n');
            result = result.trim();

            // 5. 如果结果太短或为空，尝试提取最后的实质性内容
            if (result.length < 20) {
                // 按段落分割，取最后几个有实质内容的段落
                const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 10);
                if (paragraphs.length > 0) {
                    // 取最后1-2个段落作为回答
                    result = paragraphs.slice(-2).join('\n\n');
                } else {
                    result = content; // 如果都失败了，返回原内容
                }
            }

            return result;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            loading.style.display = show ? 'block' : 'none';
        }

        function setSendButtonState(enabled) {
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = !enabled;
            sendButton.textContent = enabled ? '发送' : '发送中...';
        }

        function showError(message) {
            const chatMessages = document.getElementById('chatMessages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            chatMessages.appendChild(errorDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 点击模态窗口外部关闭对话框
        window.onclick = function(event) {
            const modal = document.getElementById('chatModal');
            if (event.target === modal) {
                closeChat();
            }
        }
    </script>
</body>
</html>
